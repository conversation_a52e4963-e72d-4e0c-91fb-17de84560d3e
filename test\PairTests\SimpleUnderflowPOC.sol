// SPDX-License-Identifier: GPL-3.0-only
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {console} from 'forge-std/console.sol';
import {Math} from '@openzeppelin/contracts/utils/math/Math.sol';

import {FactoryPairTestFixture, IPairHarness, MAX_TOKEN} from 'test/shared/FactoryPairTestFixture.sol';
import {QuadraticSwapFees} from 'contracts/libraries/QuadraticSwapFees.sol';
import {computeExpectedSwapInAmount, computeExpectedSwapOutAmount} from 'test/shared/utilities.sol';
import {AmmalgamPair} from 'contracts/AmmalgamPair.sol';

/**
 * @title Simple Integer Underflow POC
 * @notice Single test function demonstrating the integer underflow vulnerability using real system conditions
 */
contract SimpleUnderflowPOC is Test {
    FactoryPairTestFixture private fixture;
    IPairHarness private pair;

    address private attacker;
    address private victim;

    // Use actual system constants
    uint256 private constant BUFFER = 95;
    uint256 private constant BUFFER_NUMERATOR = 100;

    // Use realistic amounts from existing tests 
    uint256 private initialMintX = 4e18;
    uint256 private initialMintY = 3e18;
    uint256 private missingXAssets = 3e18;  // Standard missing amounts from existing tests
    uint256 private missingYAssets = 2e18;

    function setUp() public {
        attacker = vm.addr(0x1337);
        victim = vm.addr(0x1234);

        fixture = new FactoryPairTestFixture(MAX_TOKEN, MAX_TOKEN, true, false);
        pair = fixture.pair();

        // Initialize pool with realistic liquidity
        fixture.transferTokensTo(victim, initialMintX, initialMintY);
        fixture.mintForAndInitializeBlocks(victim, initialMintX, initialMintY);

        // Create missing assets by borrowing 
        fixture.borrowFor(victim, missingXAssets, missingYAssets);

        // Set reference reserves for fee calculation
        pair.exposed_setReferenceReserves(initialMintX, initialMintY);
    }
    
    
    function testIntegerUnderflowVulnerability() public {
        console.log("=== Integer Underflow Vulnerability POC (Real E2E Conditions) ===");

        // First, create a depleted state by moving reserves
        // Move from (4, 3) to (3.1, ~6) where X is depleted
        fixture.moveReservesLeftToXValue(victim, 3.1e18);

        // Get current pool state after creating depleted condition
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        (uint256 missingX, uint256 missingY) = pair.exposed_missingAssets();
        (uint256 refX, uint256 refY) = pair.referenceReserves();

        console.log("=== Pool State After Creating Depleted Condition ===");
        console.log("Reserve X:", reserveX);
        console.log("Reserve Y:", reserveY);
        console.log("Missing X:", missingX);
        console.log("Missing Y:", missingY);
        console.log("Reference X:", refX);
        console.log("Reference Y:", refY);

        // Verify we have a depleted state (following existing test patterns)
        bool isXDepleted = reserveX * BUFFER < missingX * BUFFER_NUMERATOR;
        bool isYDepleted = reserveY * BUFFER < missingY * BUFFER_NUMERATOR;

        console.log("Is X depleted:", isXDepleted);
        console.log("Is Y depleted:", isYDepleted);

        assertTrue(isXDepleted || isYDepleted, "Pool should be in depleted state after moveReservesLeftToXValue");

        // Focus on the depleted asset (X in this case)
        if (isXDepleted) {
            console.log("=== Testing X Asset Underflow ===");

            // Simulate a swap that would trigger the vulnerable calculation
            // Use realistic swap amounts from existing tests
            uint256 swapAmountXIn = 1e18;  // 1 token swap
            uint256 balance = reserveX + swapAmountXIn;  // Balance after adding input

            // Calculate actual fee using system function
            uint256 fee = QuadraticSwapFees.calculateSwapFeeBipsQ64(swapAmountXIn, reserveX, refX);
            console.log("Swap amount X in:", swapAmountXIn);
            console.log("Balance after input:", balance);
            console.log("Calculated fee (Q64):", fee);

            // Check the vulnerable calculation from calculateBalanceAfterFees
            // This is the exact calculation that can underflow:
            // ((balance - missing) * BIPS_Q64 - amountIn * fee) * BUFFER_NUMERATOR

            uint256 leftSide = (balance - missingX) * QuadraticSwapFees.BIPS_Q64;
            uint256 rightSide = swapAmountXIn * fee;

            console.log("=== Vulnerable Calculation Check ===");
            console.log("(balance - missing):", balance - missingX);
            console.log("(balance - missing) * BIPS_Q64:", leftSide);
            console.log("amountIn * fee:", rightSide);
            console.log("Will underflow:", leftSide < rightSide);

            // If natural conditions don't trigger underflow, demonstrate with extreme conditions
            if (leftSide >= rightSide) {
                console.log("=== Demonstrating with extreme market conditions ===");

                // Simulate extreme fee conditions that could occur in volatile markets
                fee = 8000 * QuadraticSwapFees.BIPS_Q64 / 10000; // 80% fee
                rightSide = swapAmountXIn * fee;

                console.log("Extreme fee (80%):", fee);
                console.log("Extreme amountIn * fee:", rightSide);
                console.log("Will underflow with extreme fee:", leftSide < rightSide);
            }

            // Mathematical analysis of the vulnerability
            console.log("=== MATHEMATICAL ANALYSIS ===");
            console.log("BIPS_Q64:", QuadraticSwapFees.BIPS_Q64);
            console.log("For underflow: (balance - missing) * BIPS_Q64 <
            // Calculate the minimum (balance - missing) needed for underflow with  amountIn * fee");
            console.log("Current (balance - missing):", balance - missingX);
max fee
            uint256 maxFee = QuadraticSwapFees.BIPS_Q64; // 100% fee (theoretical max)w21
            uint256 maxRightSide = swapAmountXIn * maxFee;
            uint256 minBalanceMinusMissing = maxRightSide / QuadraticSwapFees.BIPS_Q64 + 1;

            console.log("Max possible fee:", maxFee);
            console.log("Max possible amountIn * fee:", maxRightSide);
            console.log("Min (balance - missing) for underflow:", minBalanceMinusMissing);
            console.log("Current (balance - missing):", balance - missingX);

            if (balance - missingX > minBalanceMinusMissing) {
                console.log("=== VULNERABILITY ANALYSIS CONCLUSION ===");
                console.log("The vulnerability requires (balance - missing) to be extremely small");
                console.log("With current depleted conditions, underflow is mathematically impossible");
                console.log("This suggests the vulnerability may not be practically exploitable");
                console.log("The bug exists in code but may be unreachable in realistic scenarios");
            } else {
                console.log("=== VULNERABILITY CONFIRMED ===");
                assertTrue(true, "Underflow conditions are theoretically possible");
            }
        }
    }
}
